import React, { useState, useEffect } from 'react';
import { apiGet } from '../utils/apiUtils';
import './UploadReport.css';

/**
 * Upload Report Component
 * Báo cáo kết quả upload session với:
 * - <PERSON><PERSON><PERSON> quan kết quả (thành công/thất bại/bỏ qua)
 * - <PERSON> tiết từng file
 * - Thống kê performance
 * - Export báo cáo
 */
const UploadReport = ({ session, onBack, onNewUpload }) => {
    const [uploadItems, setUploadItems] = useState([]);
    const [filteredItems, setFilteredItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filter, setFilter] = useState('all'); // all, uploaded, failed, skipped
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('upload_completed_at'); // upload_completed_at, file_name, file_size, user_email
    const [sortOrder, setSortOrder] = useState('desc'); // asc, desc
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(50);

    useEffect(() => {
        loadUploadItems();
    }, [session.id]);

    useEffect(() => {
        filterAndSortItems();
    }, [uploadItems, filter, searchTerm, sortBy, sortOrder]);

    /**
     * Load upload items - load all items in batches
     */
    const loadUploadItems = async () => {
        try {
            setLoading(true);

            // Load all items in batches
            let allItems = [];
            let page = 1;
            let hasMore = true;
            const pageSize = 1000;

            while (hasMore) {
                const response = await apiGet(`/api/upload/sessions/${session.id}/items?page=${page}&limit=${pageSize}`);

                if (response.items && response.items.length > 0) {
                    allItems.push(...response.items);
                    hasMore = response.pagination && response.pagination.hasNextPage;
                    page++;
                } else {
                    hasMore = false;
                }
            }

            setUploadItems(allItems);
        } catch (error) {
            console.error('Error loading upload items:', error);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Filter and sort items
     */
    const filterAndSortItems = () => {
        let filtered = uploadItems;

        // Apply filter
        if (filter !== 'all') {
            filtered = filtered.filter(item => item.status === filter);
        }

        // Apply search
        if (searchTerm) {
            const searchLower = searchTerm.toLowerCase();
            filtered = filtered.filter(item =>
                item.file_name.toLowerCase().includes(searchLower) ||
                item.user_email.toLowerCase().includes(searchLower) ||
                (item.error_message && item.error_message.toLowerCase().includes(searchLower))
            );
        }

        // Apply sort
        filtered.sort((a, b) => {
            let aValue = a[sortBy];
            let bValue = b[sortBy];

            // Handle different data types
            if (sortBy === 'file_size') {
                aValue = aValue || 0;
                bValue = bValue || 0;
            } else if (sortBy === 'upload_completed_at') {
                aValue = aValue ? new Date(aValue) : new Date(0);
                bValue = bValue ? new Date(bValue) : new Date(0);
            } else if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        setFilteredItems(filtered);
        setCurrentPage(1); // Reset to first page when filtering
    };

    /**
     * Get paginated items
     */
    const getPaginatedItems = () => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filteredItems.slice(startIndex, endIndex);
    };

    /**
     * Calculate statistics
     */
    const getStatistics = () => {
        const total = uploadItems.length;
        const uploaded = uploadItems.filter(item => item.status === 'uploaded').length;
        const failed = uploadItems.filter(item => item.status === 'failed').length;
        const skipped = uploadItems.filter(item => item.status === 'skipped').length;

        const totalSize = uploadItems.reduce((sum, item) => sum + (item.file_size || 0), 0);
        const uploadedSize = uploadItems
            .filter(item => item.status === 'uploaded')
            .reduce((sum, item) => sum + (item.file_size || 0), 0);

        const uploadedItems = uploadItems.filter(item => item.status === 'uploaded' && item.upload_duration);
        const avgUploadTime = uploadedItems.length > 0
            ? uploadedItems.reduce((sum, item) => sum + item.upload_duration, 0) / uploadedItems.length
            : 0;

        const duration = session.completed_at && session.started_at
            ? new Date(session.completed_at) - new Date(session.started_at)
            : 0;

        return {
            total,
            uploaded,
            failed,
            skipped,
            totalSize,
            uploadedSize,
            avgUploadTime,
            duration,
            successRate: total > 0 ? (uploaded / total) * 100 : 0
        };
    };

    /**
     * Format file size
     */
    const formatFileSize = (bytes) => {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    /**
     * Format duration
     */
    const formatDuration = (ms) => {
        if (!ms) return 'N/A';

        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    };

    /**
     * Get status icon
     */
    const getStatusIcon = (status) => {
        switch (status) {
            case 'uploaded': return '✅';
            case 'failed': return '❌';
            case 'skipped': return '⏭️';
            default: return '❓';
        }
    };

    /**
     * Export report to CSV
     */
    const exportToCSV = () => {
        const headers = [
            'File Name',
            'User Email',
            'File Size',
            'Status',
            'Upload Duration',
            'Error Message',
            'Upload Completed At'
        ];

        const csvData = uploadItems.map(item => [
            item.file_name,
            item.user_email,
            item.file_size || 0,
            item.status,
            item.upload_duration || '',
            item.error_message || '',
            item.upload_completed_at || ''
        ]);

        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `upload-report-${session.name}-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const stats = getStatistics();
    const paginatedItems = getPaginatedItems();
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);

    if (loading) {
        return (
            <div className="upload-report loading">
                <div className="loading-spinner"></div>
                <p>Đang tải báo cáo...</p>
            </div>
        );
    }

    return (
        <div className="upload-report">
            {/* Header */}
            <div className="report-header">
                <div className="session-info">
                    <h2>📊 Báo cáo Upload: {session.name}</h2>
                    <div className="session-meta">
                        <span className={`status-badge status-${session.status}`}>
                            {session.status.toUpperCase()}
                        </span>
                        <span className="session-date">
                            {new Date(session.completed_at || session.created_at).toLocaleString()}
                        </span>
                    </div>
                </div>

                <div className="report-actions">
                    <button onClick={exportToCSV} className="btn btn-secondary">
                        📄 Export CSV
                    </button>
                    <button onClick={onNewUpload} className="btn btn-primary">
                        🚀 Upload mới
                    </button>
                    <button onClick={onBack} className="btn btn-secondary">
                        ← Quay lại
                    </button>
                </div>
            </div>

            {/* Statistics Overview */}
            <div className="statistics-overview">
                <div className="stats-grid">
                    <div className="stat-card success">
                        <div className="stat-icon">✅</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.uploaded}</div>
                            <div className="stat-label">Thành công</div>
                        </div>
                    </div>

                    <div className="stat-card error">
                        <div className="stat-icon">❌</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.failed}</div>
                            <div className="stat-label">Thất bại</div>
                        </div>
                    </div>

                    <div className="stat-card skipped">
                        <div className="stat-icon">⏭️</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.skipped}</div>
                            <div className="stat-label">Bỏ qua</div>
                        </div>
                    </div>

                    <div className="stat-card total">
                        <div className="stat-icon">📁</div>
                        <div className="stat-content">
                            <div className="stat-value">{stats.total}</div>
                            <div className="stat-label">Tổng files</div>
                        </div>
                    </div>
                </div>

                <div className="stats-details">
                    <div className="detail-item">
                        <span className="detail-label">Tỷ lệ thành công:</span>
                        <span className="detail-value">{stats.successRate.toFixed(1)}%</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Dung lượng đã upload:</span>
                        <span className="detail-value">{formatFileSize(stats.uploadedSize)}</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Tổng dung lượng:</span>
                        <span className="detail-value">{formatFileSize(stats.totalSize)}</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Thời gian upload trung bình:</span>
                        <span className="detail-value">{formatDuration(stats.avgUploadTime)}</span>
                    </div>
                    <div className="detail-item">
                        <span className="detail-label">Tổng thời gian:</span>
                        <span className="detail-value">{formatDuration(stats.duration)}</span>
                    </div>
                </div>
            </div>

            {/* Filters and Search */}
            <div className="filters-section">
                <div className="filters-row">
                    <div className="filter-group">
                        <label>Lọc theo trạng thái:</label>
                        <select value={filter} onChange={(e) => setFilter(e.target.value)}>
                            <option value="all">Tất cả ({uploadItems.length})</option>
                            <option value="uploaded">Thành công ({stats.uploaded})</option>
                            <option value="failed">Thất bại ({stats.failed})</option>
                            <option value="skipped">Bỏ qua ({stats.skipped})</option>
                        </select>
                    </div>

                    <div className="filter-group">
                        <label>Sắp xếp theo:</label>
                        <select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
                            <option value="upload_completed_at">Thời gian</option>
                            <option value="file_name">Tên file</option>
                            <option value="file_size">Kích thước</option>
                            <option value="user_email">User</option>
                        </select>
                    </div>

                    <div className="filter-group">
                        <label>Thứ tự:</label>
                        <select value={sortOrder} onChange={(e) => setSortOrder(e.target.value)}>
                            <option value="desc">Giảm dần</option>
                            <option value="asc">Tăng dần</option>
                        </select>
                    </div>

                    <div className="search-group">
                        <input
                            type="text"
                            placeholder="Tìm kiếm file, user, lỗi..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                        />
                    </div>
                </div>
            </div>

            {/* Items Table */}
            <div className="items-table-container">
                <div className="table-header">
                    <h3>📋 Chi tiết files ({filteredItems.length} kết quả)</h3>
                </div>

                <div className="items-table">
                    <div className="table-header-row">
                        <div className="table-cell">Trạng thái</div>
                        <div className="table-cell">Tên file</div>
                        <div className="table-cell">User</div>
                        <div className="table-cell">Kích thước</div>
                        <div className="table-cell">Thời gian upload</div>
                        <div className="table-cell">Lỗi</div>
                    </div>

                    {paginatedItems.map((item, index) => (
                        <div key={item.id} className={`table-row ${item.status}`}>
                            <div className="table-cell status-cell">
                                <span className="status-icon">{getStatusIcon(item.status)}</span>
                                <span className="status-text">{item.status}</span>
                            </div>
                            <div className="table-cell file-cell">
                                <div className="file-name">{item.file_name}</div>
                                {item.lark_file_token && (
                                    <div className="lark-info">Lark: {item.lark_file_token.substring(0, 20)}...</div>
                                )}
                            </div>
                            <div className="table-cell">{item.user_email}</div>
                            <div className="table-cell">{formatFileSize(item.file_size)}</div>
                            <div className="table-cell">
                                {item.upload_duration ? formatDuration(item.upload_duration) : 'N/A'}
                            </div>
                            <div className="table-cell error-cell">
                                {item.error_message && (
                                    <span className="error-message" title={item.error_message}>
                                        {item.error_message.length > 50
                                            ? item.error_message.substring(0, 50) + '...'
                                            : item.error_message
                                        }
                                    </span>
                                )}
                            </div>
                        </div>
                    ))}

                    {paginatedItems.length === 0 && (
                        <div className="no-results">
                            <p>Không tìm thấy kết quả nào.</p>
                        </div>
                    )}
                </div>

                {/* Pagination */}
                <div className="pagination-container">
                    <div className="pagination-info-left">
                        <span>
                            Hiển thị {Math.min((currentPage - 1) * itemsPerPage + 1, filteredItems.length)} - {Math.min(currentPage * itemsPerPage, filteredItems.length)}
                            trong tổng số {filteredItems.length} kết quả
                        </span>
                    </div>

                    <div className="pagination-controls">
                        <div className="items-per-page">
                            <label>Hiển thị:</label>
                            <select
                                value={itemsPerPage}
                                onChange={(e) => {
                                    setItemsPerPage(Number(e.target.value));
                                    setCurrentPage(1);
                                }}
                            >
                                <option value={25}>25</option>
                                <option value={50}>50</option>
                                <option value={100}>100</option>
                                <option value={200}>200</option>
                            </select>
                            <span>/ trang</span>
                        </div>

                        {totalPages > 1 && (
                            <div className="pagination">
                                <button
                                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                    disabled={currentPage === 1}
                                    className="btn btn-secondary btn-sm"
                                >
                                    ← Trước
                                </button>

                                <span className="pagination-info">
                                    Trang {currentPage} / {totalPages}
                                </span>

                                <button
                                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                    disabled={currentPage === totalPages}
                                    className="btn btn-secondary btn-sm"
                                >
                                    Sau →
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UploadReport;
