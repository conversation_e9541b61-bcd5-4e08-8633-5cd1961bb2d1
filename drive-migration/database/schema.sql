-- Drive-to-Lark Migration Database Schema
-- T<PERSON><PERSON> các bảng cần thiết cho hệ thống migration

-- Bảng mapping người dùng Google -> Lark
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email_google TEXT UNIQUE NOT NULL,
  lark_userid TEXT,
  mapped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: scanned_users
-- <PERSON><PERSON><PERSON> thông tin user đã scan từ Google Workspace
CREATE TABLE scanned_users (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL UNIQUE, -- Google user ID
  primary_email TEXT NOT NULL UNIQUE,
  given_name TEXT,
  family_name TEXT,
  full_name TEXT,
  last_login_time TIMESTAMPTZ,
  suspended BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table: user_storage_stats
-- <PERSON><PERSON><PERSON> thông tin dung lượng sử dụng của user trên Google Drive và Gmail
CREATE TABLE user_storage_stats (
  id SERIAL PRIMARY KEY,
  user_email TEXT NOT NULL UNIQUE REFERENCES scanned_users(primary_email),
  -- Google Drive storage info
  drive_usage_bytes BIGINT DEFAULT 0, -- Dung lượng đã sử dụng trên Drive
  drive_limit_bytes BIGINT DEFAULT 0, -- Giới hạn dung lượng Drive (nếu có)
  -- Gmail storage info
  gmail_usage_bytes BIGINT DEFAULT 0, -- Dung lượng đã sử dụng Gmail
  -- Total Google storage info
  total_usage_bytes BIGINT DEFAULT 0, -- Tổng dung lượng đã sử dụng
  total_limit_bytes BIGINT DEFAULT 0, -- Tổng giới hạn dung lượng
  -- Scanned files storage info
  scanned_storage_bytes BIGINT DEFAULT 0, -- Tổng dung lượng files đã scan (từ bảng scanned_files)
  scanned_storage_updated_at TIMESTAMP WITH TIME ZONE, -- Thời gian cập nhật cuối scanned storage
  -- Local download tracking
  local_downloaded_bytes BIGINT DEFAULT 0, -- Tổng dung lượng đã download về local
  local_folder_path TEXT DEFAULT 'E:\', -- Đường dẫn thư mục local
  -- Metadata
  last_scanned_at TIMESTAMPTZ, -- Lần cuối cùng scan storage info
  scan_error_message TEXT, -- Lỗi khi scan (nếu có)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng scan sessions cho Drive scanning
CREATE TABLE scan_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_email TEXT NOT NULL,
  scan_type TEXT NOT NULL CHECK (scan_type IN ('full_drive', 'folder_specific', 'selective')),
  scan_options JSONB, -- Lưu options như maxDepth, filterMimeTypes, etc.
  status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  scanned_files INTEGER DEFAULT 0,
  current_depth INTEGER DEFAULT 0,
  folders_processed INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  scan_duration INTEGER, -- milliseconds
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT
);

-- Bảng lưu trữ files đã scan
-- First, create the enum type for scanned file status
CREATE TYPE scanned_file_status AS ENUM ('Downloaded', 'UploadedToLark', 'ApplyPermissionToLark', 'Completed', 'Error');

CREATE TABLE scanned_files (
  id BIGSERIAL PRIMARY KEY,
  scan_session_id UUID NOT NULL REFERENCES scan_sessions(id) ON DELETE CASCADE,
  file_id TEXT NOT NULL, -- Google Drive file ID
  name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  size BIGINT DEFAULT 0,
  full_path TEXT NOT NULL, -- Đường dẫn đầy đủ
  depth INTEGER NOT NULL DEFAULT 0,
  parents TEXT[], -- Array of parent folder IDs
  created_time TIMESTAMPTZ,
  modified_time TIMESTAMPTZ,
  owners JSONB, -- Google Drive owners info
  permissions JSONB, -- Google Drive permissions
  web_view_link TEXT,
  metadata JSONB, -- Additional metadata (iconLink, thumbnailLink, etc.)
  is_selected BOOLEAN DEFAULT FALSE, -- Được chọn để migrate
  user_email TEXT NOT NULL REFERENCES scanned_users(primary_email),
  domain TEXT NOT NULL,
  status scanned_file_status,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Thêm cột upload_status vào bảng scanned_files
ALTER TABLE scanned_files
ADD COLUMN IF NOT EXISTS upload_status TEXT CHECK (upload_status IN ('not_uploaded', 'uploading', 'uploaded', 'failed')) DEFAULT 'not_uploaded',
ADD COLUMN IF NOT EXISTS lark_file_token TEXT,
ADD COLUMN IF NOT EXISTS lark_folder_token TEXT,
ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS upload_error_message TEXT;

-- Bảng upload_sessions: Quản lý các session upload lên Lark
CREATE TABLE IF NOT EXISTS upload_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  selected_users TEXT[] NOT NULL, -- Array of user emails
  root_folder_path TEXT NOT NULL, -- Local root folder path
  lark_target_folder TEXT, -- Target folder in Lark (optional)
  concurrent_uploads INTEGER DEFAULT 10,
  max_retries INTEGER DEFAULT 1,
  skip_mime_types TEXT[] DEFAULT '{}', -- Array of MIME types to skip
  max_file_size BIGINT DEFAULT 104857600, -- 100MB default limit
  duplicate_handling TEXT DEFAULT 'skip' CHECK (duplicate_handling IN ('skip', 'overwrite', 'rename')),
  batch_size INTEGER DEFAULT 50,
  bandwidth_limit INTEGER, -- KB/s limit (optional)
  validate_upload BOOLEAN DEFAULT true,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  uploaded_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  skipped_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  uploaded_size BIGINT DEFAULT 0,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  options JSONB -- Additional configuration options
);

-- Bảng upload_items: Track chi tiết từng file trong session upload
CREATE TABLE IF NOT EXISTS upload_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  upload_session_id UUID NOT NULL REFERENCES upload_sessions(id) ON DELETE CASCADE,
  scanned_file_id BIGINT REFERENCES scanned_files(id),
  user_email TEXT NOT NULL,
  file_id TEXT NOT NULL, -- Google Drive file ID
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- Path trong Google Drive
  local_path TEXT NOT NULL, -- Local file path
  lark_file_token TEXT, -- Lark file token sau khi upload
  lark_file_name TEXT, -- File name in Lark
  lark_folder_token TEXT, -- Lark folder token
  file_size BIGINT DEFAULT 0,
  mime_type TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'uploading', 'uploaded', 'failed', 'skipped')),
  retry_count INTEGER DEFAULT 0,
  error_message TEXT,
  upload_started_at TIMESTAMPTZ,
  upload_completed_at TIMESTAMPTZ,
  upload_duration INTEGER, -- milliseconds
  validation_status TEXT CHECK (validation_status IN ('pending', 'passed', 'failed')),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng tác vụ migration
CREATE TABLE migration_tasks (
  id UUID PRIMARY KEY, -- Custom migration ID
  owner_email TEXT NOT NULL,
  scan_session_id UUID REFERENCES scan_sessions(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'completed_with_errors', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  processed_files INTEGER DEFAULT 0,
  successful_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0, -- Tổng kích thước file (bytes)
  processed_size BIGINT DEFAULT 0, -- Đã xử lý (bytes)
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  error_message TEXT,
  options JSONB, -- Migration options (mapPermissions, targetRootFolder, etc.)
  checkpoint_data JSONB -- Checkpoint data for resume capability
);

-- Bảng chi tiết từng file migration
CREATE TABLE migration_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  migration_task_id UUID NOT NULL REFERENCES migration_tasks(id) ON DELETE CASCADE,
  google_file_id TEXT NOT NULL, -- Google Drive file ID
  google_file_name TEXT NOT NULL,
  google_file_path TEXT NOT NULL, -- Đường dẫn đầy đủ trong Drive
  google_file_size BIGINT DEFAULT 0,
  lark_file_token TEXT, -- Lark Drive file token sau khi upload
  lark_file_name TEXT, -- File name in Lark
  lark_folder_token TEXT, -- Lark folder token
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'uploading', 'completed', 'failed', 'skipped')),
  error_message TEXT,
  download_time INTEGER, -- Download time in milliseconds
  upload_time INTEGER, -- Upload time in milliseconds
  retries INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB -- Lưu metadata bổ sung (permissions, checksums, etc.)
);

-- Bảng mapping quyền truy cập
CREATE TABLE permission_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  migration_item_id UUID NOT NULL REFERENCES migration_items(id) ON DELETE CASCADE,
  google_email TEXT NOT NULL,
  google_role TEXT NOT NULL, -- owner, writer, reader, commenter
  lark_user_id TEXT, -- Mapped Lark user ID
  lark_role TEXT, -- full_access, edit, view, comment
  mapped BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng log hệ thống
CREATE TABLE migration_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES migration_tasks(id) ON DELETE CASCADE,
  item_id UUID REFERENCES migration_items(id) ON DELETE CASCADE,
  level TEXT NOT NULL CHECK (level IN ('info', 'warning', 'error', 'debug')),
  message TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes để tối ưu performance
CREATE INDEX idx_migration_tasks_owner_email ON migration_tasks(owner_email);
CREATE INDEX idx_migration_tasks_status ON migration_tasks(status);
CREATE INDEX idx_migration_tasks_created_at ON migration_tasks(created_at);

CREATE INDEX idx_migration_items_task_id ON migration_items(migration_task_id);
CREATE INDEX idx_migration_items_status ON migration_items(status);
CREATE INDEX idx_migration_items_src_file_id ON migration_items(google_file_id);

CREATE INDEX idx_permission_mappings_migration_item_id ON permission_mappings(migration_item_id);
CREATE INDEX idx_permission_mappings_google_email ON permission_mappings(google_email);
CREATE INDEX idx_permission_mappings_mapped ON permission_mappings(mapped);

CREATE INDEX idx_migration_logs_task_id ON migration_logs(task_id);
CREATE INDEX idx_migration_logs_level ON migration_logs(level);
CREATE INDEX idx_migration_logs_created_at ON migration_logs(created_at);

CREATE INDEX idx_users_email_google ON users(email_google);
CREATE INDEX idx_users_mapped ON users(mapped);

-- Indexes for user_storage_stats
CREATE INDEX idx_user_storage_stats_user_email ON user_storage_stats(user_email);
CREATE INDEX idx_user_storage_stats_last_scanned ON user_storage_stats(last_scanned_at);
CREATE INDEX idx_user_storage_stats_total_usage ON user_storage_stats(total_usage_bytes);
CREATE INDEX idx_user_storage_stats_drive_usage ON user_storage_stats(drive_usage_bytes);

-- Triggers để tự động cập nhật updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_migration_tasks_updated_at BEFORE UPDATE ON migration_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_migration_items_updated_at BEFORE UPDATE ON migration_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_permission_mappings_updated_at BEFORE UPDATE ON permission_mappings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_storage_stats_updated_at BEFORE UPDATE ON user_storage_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views để dễ dàng query thống kê
CREATE VIEW migration_task_stats AS
SELECT 
    t.id,
    t.owner_email,
    t.status,
    t.processed_files,
    t.total_files,
    t.successful_files,
    t.failed_files,
    CASE 
        WHEN t.total_files > 0 THEN ROUND((t.successful_files::NUMERIC / t.total_files::NUMERIC) * 100, 2)
        ELSE 0 
    END as completion_percentage,
    t.total_size,
    t.processed_size,
    CASE 
        WHEN t.total_size > 0 THEN ROUND((t.processed_size::NUMERIC / t.total_size::NUMERIC) * 100, 2)
        ELSE 0 
    END as transfer_percentage,
    t.started_at,
    t.completed_at,
    CASE 
        WHEN t.started_at IS NOT NULL AND t.completed_at IS NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - t.started_at))
        WHEN t.started_at IS NOT NULL AND t.completed_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (t.completed_at - t.started_at))
        ELSE NULL 
    END as duration_seconds
FROM migration_tasks t;

-- Indexes for scan_sessions
CREATE INDEX idx_scan_sessions_user_email ON scan_sessions(user_email);
CREATE INDEX idx_scan_sessions_status ON scan_sessions(status);
CREATE INDEX idx_scan_sessions_started_at ON scan_sessions(started_at);

-- Indexes for scanned_files
CREATE INDEX idx_scanned_files_session_id ON scanned_files(scan_session_id);
CREATE INDEX idx_scanned_files_file_id ON scanned_files(file_id);
CREATE INDEX idx_scanned_files_mime_type ON scanned_files(mime_type);
CREATE INDEX idx_scanned_files_full_path ON scanned_files(full_path);
CREATE INDEX idx_scanned_files_is_selected ON scanned_files(is_selected);
CREATE INDEX idx_scanned_files_depth ON scanned_files(depth);

-- RLS (Row Level Security) policies
ALTER TABLE migration_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE migration_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE scan_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE scanned_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE migration_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_storage_stats ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own migration tasks
CREATE POLICY "Users can view own migration tasks" ON migration_tasks
    FOR SELECT USING (owner_email = auth.email());

CREATE POLICY "Users can insert own migration tasks" ON migration_tasks
    FOR INSERT WITH CHECK (owner_email = auth.email());

CREATE POLICY "Users can update own migration tasks" ON migration_tasks
    FOR UPDATE USING (owner_email = auth.email());

-- Policy: Users can only see migration items of their tasks
CREATE POLICY "Users can view own migration items" ON migration_items
    FOR SELECT USING (
        id IN (SELECT id FROM migration_tasks WHERE owner_email = auth.email())
    );

-- Indexes để tối ưu performance cho upload
CREATE INDEX IF NOT EXISTS idx_upload_sessions_status ON upload_sessions(status);
CREATE INDEX IF NOT EXISTS idx_upload_sessions_created_at ON upload_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_upload_items_session_id ON upload_items(upload_session_id);
CREATE INDEX IF NOT EXISTS idx_upload_items_status ON upload_items(status);
CREATE INDEX IF NOT EXISTS idx_upload_items_user_email ON upload_items(user_email);
CREATE INDEX IF NOT EXISTS idx_scanned_files_upload_status ON scanned_files(upload_status);
CREATE INDEX IF NOT EXISTS idx_scanned_files_local_path ON scanned_files(local_path) WHERE local_path IS NOT NULL;

-- Trigger để auto-update timestamps cho upload_sessions
CREATE OR REPLACE FUNCTION update_upload_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_upload_sessions_updated_at
    BEFORE UPDATE ON upload_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_upload_sessions_updated_at();

-- View để thống kê upload sessions
CREATE OR REPLACE VIEW upload_session_stats AS
SELECT
    us.id,
    us.name,
    us.status,
    us.total_files,
    us.uploaded_files,
    us.failed_files,
    us.skipped_files,
    us.total_size,
    us.uploaded_size,
    CASE
        WHEN us.total_files > 0 THEN
            ROUND((us.uploaded_files::DECIMAL / us.total_files::DECIMAL) * 100, 2)
        ELSE 0
    END as progress_percentage,
    CASE
        WHEN us.total_size > 0 THEN
            ROUND((us.uploaded_size::DECIMAL / us.total_size::DECIMAL) * 100, 2)
        ELSE 0
    END as size_progress_percentage,
    us.started_at,
    us.completed_at,
    CASE
        WHEN us.completed_at IS NOT NULL AND us.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (us.completed_at - us.started_at))::INTEGER
        WHEN us.started_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (NOW() - us.started_at))::INTEGER
        ELSE NULL
    END as duration_seconds,
    us.created_at,
    us.updated_at
FROM upload_sessions us;

-- View để thống kê upload theo user
CREATE OR REPLACE VIEW upload_user_stats AS
SELECT
    ui.user_email,
    COUNT(*) as total_files,
    COUNT(*) FILTER (WHERE ui.status = 'uploaded') as uploaded_files,
    COUNT(*) FILTER (WHERE ui.status = 'failed') as failed_files,
    COUNT(*) FILTER (WHERE ui.status = 'skipped') as skipped_files,
    SUM(ui.file_size) as total_size,
    SUM(ui.file_size) FILTER (WHERE ui.status = 'uploaded') as uploaded_size,
    AVG(ui.upload_duration) FILTER (WHERE ui.status = 'uploaded' AND ui.upload_duration IS NOT NULL) as avg_upload_duration
FROM upload_items ui
GROUP BY ui.user_email;

COMMENT ON TABLE upload_sessions IS 'Quản lý các session upload files lên Lark Drive';
COMMENT ON TABLE upload_items IS 'Track chi tiết từng file trong session upload';
COMMENT ON COLUMN upload_sessions.selected_users IS 'Array email của users được chọn để upload';
COMMENT ON COLUMN upload_sessions.root_folder_path IS 'Đường dẫn thư mục gốc chứa files cần upload';
COMMENT ON COLUMN upload_sessions.lark_target_folder IS 'Thư mục đích trong Lark Drive';
COMMENT ON COLUMN upload_items.local_path IS 'Đường dẫn file local cần upload';
COMMENT ON COLUMN upload_items.lark_file_token IS 'Token của file sau khi upload lên Lark';

-- Policy: Service role can access all data
CREATE POLICY "Service role full access migration_tasks" ON migration_tasks
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access migration_items" ON migration_items
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access permission_mappings" ON permission_mappings
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access migration_logs" ON migration_logs
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access users" ON users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access user_storage_stats" ON user_storage_stats
    FOR ALL USING (auth.role() = 'service_role');

-- Realtime subscriptions
ALTER PUBLICATION supabase_realtime ADD TABLE migration_tasks;
ALTER PUBLICATION supabase_realtime ADD TABLE migration_items;
ALTER PUBLICATION supabase_realtime ADD TABLE migration_logs;

-- Function để lấy migration statistics
CREATE OR REPLACE FUNCTION get_migration_stats(task_id TEXT)
RETURNS TABLE (
    total_files INTEGER,
    completed_files INTEGER,
    failed_files INTEGER,
    in_progress_files INTEGER,
    pending_files INTEGER,
    success_rate DECIMAL,
    total_size BIGINT,
    processed_size BIGINT,
    avg_processing_time DECIMAL,
    total_processing_time BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_files,
        COUNT(CASE WHEN mi.status = 'completed' THEN 1 END)::INTEGER as completed_files,
        COUNT(CASE WHEN mi.status = 'failed' THEN 1 END)::INTEGER as failed_files,
        COUNT(CASE WHEN mi.status IN ('downloading', 'uploading') THEN 1 END)::INTEGER as in_progress_files,
        COUNT(CASE WHEN mi.status = 'pending' THEN 1 END)::INTEGER as pending_files,
        CASE
            WHEN COUNT(*) > 0 THEN
                ROUND((COUNT(CASE WHEN mi.status = 'completed' THEN 1 END)::DECIMAL / COUNT(*)) * 100, 2)
            ELSE 0
        END as success_rate,
        COALESCE(SUM(mi.google_file_size), 0) as total_size,
        COALESCE(SUM(CASE WHEN mi.status = 'completed' THEN mi.google_file_size ELSE 0 END), 0) as processed_size,
        CASE
            WHEN COUNT(CASE WHEN mi.status = 'completed' THEN 1 END) > 0 THEN
                ROUND(AVG(CASE WHEN mi.status = 'completed' THEN (COALESCE(mi.download_time, 0) + COALESCE(mi.upload_time, 0)) END), 2)
            ELSE 0
        END as avg_processing_time,
        COALESCE(SUM(CASE WHEN mi.status = 'completed' THEN (COALESCE(mi.download_time, 0) + COALESCE(mi.upload_time, 0)) ELSE 0 END), 0) as total_processing_time
    FROM migration_items mi
    WHERE mi.migration_task_id = task_id;
END;
$$ LANGUAGE plpgsql;
